import { api } from "@/../convex/_generated/api";
import { Doc, Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import { Input } from "@/components/_shared/Input";
import { Label } from "@/components/_shared/Label";
import { AppDialogLayout } from "@/components/layout/AppDialogLayout";
import { Textarea } from "@/components/_shared/Textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import {
	calculateHours,
	formatCurrency,
	formatDate,
	formatHours,
	formatTime,
	toInputDate,
	toInputTime,
} from "@/lib/utils/formatUtils";
import { useMutation, useQuery } from "convex/react";
import {
	AlertCircle,
	Check,
	Pencil,
	Plus,
	PlusCircle,
	Split,
	Trash2,
} from "lucide-react";
import { useState, useEffect, useMemo } from "react";
import { useNavigate } from "react-router-dom";
import { toast } from "sonner";

// Interfaces
interface LeistungMitNamen {
	_id: Id<"kunden_leistungen">;
	kundenId: Id<"kunden">;
	mitarbeiterId: Id<"mitarbeiter">;
	kontingentId: Id<"kunden_kontingente">;
	kontingentId2?: Id<"kunden_kontingente">;
	startZeit: number;
	endZeit: number;
	art: string;
	beschreibung: string;
	stundenpreis: number;
	anfahrtskosten: number;
}
interface KontingentOption {
	_id: Id<"kunden_kontingente">;
	name: string;
	startDatum: number;
	stunden: number;
	verbrauchteStunden: number;
}
type KundeDoc = Doc<"kunden">;
type MitarbeiterDoc = Doc<"mitarbeiter">;

type LeistungFormData = {
	id: string;
	kundeId: string;
	mitarbeiterId: string;
	kontingentId: string;
	kontingentId2: string;
	datum: string;
	startUhrzeit: string;
	endUhrzeit: string;
	art: "remote" | "vor-Ort" | "vor-Ort (free)";
	stundenpreisInput: string;
	anfahrtskostenInput: string;
	beschreibung: string;
};

interface LeistungFormProps {
	initialData: LeistungFormData & { _id?: Id<"kunden_leistungen"> };
	isEditing: boolean;
	kunden: KundeDoc[];
	mitarbeiter: MitarbeiterDoc[];
	onSubmitSuccess: () => void;
	onCancel: () => void;
}

const createNewLeistungItem = (
	kunden: KundeDoc[],
	defaultKundeId = "",
): LeistungFormData => {
	const now = new Date();
	const startTime = new Date(now.getTime() - 60 * 60 * 1000);
	const selectedKunde = kunden.find((k) => k._id === defaultKundeId);
	return {
		id: crypto.randomUUID(),
		kundeId: defaultKundeId,
		mitarbeiterId: "",
		kontingentId: "",
		kontingentId2: "",
		datum: toInputDate(now.getTime()),
		startUhrzeit: toInputTime(startTime.getTime()),
		endUhrzeit: toInputTime(now.getTime()),
		art: "remote",
		stundenpreisInput: selectedKunde
			? selectedKunde.stundenpreis.toString()
			: "",
		anfahrtskostenInput: selectedKunde
			? selectedKunde.anfahrtskosten.toString()
			: "",
		beschreibung: "",
	};
};

export function LeistungForm({
	initialData,
	isEditing,
	kunden,
	mitarbeiter,
	onSubmitSuccess,
	onCancel,
}: LeistungFormProps) {
	const createLeistung = useMutation(api.erstellung.leistung.create);
	const updateLeistung = useMutation(api.erstellung.leistung.update);
	const navigate = useNavigate();

	const [selectedKundeId, setSelectedKundeId] = useState<string>(() => {
		return isEditing ? initialData.kundeId : "";
	});

	const [formState, setFormState] = useState<LeistungFormData[]>(() => {
		if (isEditing) {
			return [
				{
					...initialData,
					id: initialData._id || crypto.randomUUID(),
				},
			];
		}
		return [createNewLeistungItem(kunden, selectedKundeId)];
	});

	const handleKundeChange = (kundeId: string) => {
		setSelectedKundeId(kundeId);
		const selectedKunde = kunden.find((k) => k._id === kundeId);

		setFormState((prev) =>
			prev.map((item) => ({
				...item,
				kundeId,
				kontingentId: "",
				kontingentId2: "",
				stundenpreisInput: selectedKunde
					? selectedKunde.stundenpreis.toString()
					: "",
				anfahrtskostenInput: selectedKunde
					? selectedKunde.anfahrtskosten.toString()
					: "",
			})),
		);
	};

	const handleAddItem = () => {
		setFormState([
			...formState,
			createNewLeistungItem(kunden, selectedKundeId),
		]);
	};

	const handleRemoveItem = (id: string) => {
		if (formState.length > 1) {
			setFormState(formState.filter((item) => item.id !== id));
		}
	};

	const handleFormChange = (
		id: string,
		field: keyof LeistungFormData,
		value: string,
	) => {
		setFormState((prev) =>
			prev.map((item) => (item.id === id ? { ...item, [field]: value } : item)),
		);
	};

	const totalSummary = useMemo(() => {
		return formState.reduce(
			(acc, item) => {
				const timestamps = getTimestamps(
					item.datum,
					item.startUhrzeit,
					item.endUhrzeit,
				);
				if (!timestamps) return acc;
				const stunden = calculateHours(
					timestamps.startZeit,
					timestamps.endZeit,
				);
				const preis = Number.parseFloat(item.stundenpreisInput) || 0;
				const anfahrt =
					item.art === "vor-Ort"
						? Number.parseFloat(item.anfahrtskostenInput) || 0
						: 0;
				return {
					stunden: acc.stunden + stunden,
					gesamt: acc.gesamt + stunden * preis + anfahrt,
				};
			},
			{ stunden: 0, gesamt: 0 },
		);
	}, [formState]);

	const handleSubmit = async () => {
		if (!selectedKundeId) {
			toast.error("Bitte wählen Sie einen Kunden aus.");
			return;
		}

		const promises = formState.map(async (item, index) => {
			const timestamps = getTimestamps(
				item.datum,
				item.startUhrzeit,
				item.endUhrzeit,
			);
			if (!timestamps)
				throw new Error(`Ungültiges Datum/Uhrzeit in Position ${index + 1}`);

			const { startZeit, endZeit } = timestamps;
			const {
				mitarbeiterId,
				kontingentId,
				kontingentId2,
				art,
				beschreibung,
				stundenpreisInput,
				anfahrtskostenInput,
			} = item;

			const preis = stundenpreisInput
				? Number.parseFloat(stundenpreisInput)
				: undefined;
			const anfahrt = anfahrtskostenInput
				? Number.parseFloat(anfahrtskostenInput)
				: undefined;

			if (!mitarbeiterId || !kontingentId || !beschreibung) {
				throw new Error(
					`Bitte alle Felder in Position ${index + 1} ausfüllen.`,
				);
			}

			const mutationArgs = {
				kundenId: selectedKundeId as Id<"kunden">,
				mitarbeiterId: mitarbeiterId as Id<"mitarbeiter">,
				kontingentId: kontingentId as Id<"kunden_kontingente">,
				kontingentId2: kontingentId2
					? (kontingentId2 as Id<"kunden_kontingente">)
					: undefined,
				startZeit,
				endZeit,
				beschreibung,
				art,
				stundenpreis: preis,
				anfahrtskosten: art === "vor-Ort" ? anfahrt : 0,
			};

			if (isEditing) {
				return updateLeistung({ id: initialData._id!, ...mutationArgs });
			}
			return createLeistung(mutationArgs);
		});

		try {
			const results = await Promise.all(promises);

			// Erfolgs-Toast mit Lieferschein-Button nur beim Erstellen neuer Leistungen (nicht beim Bearbeiten)
			if (!isEditing) {
				toast.success(`${results.length} Leistung(en) erfolgreich erstellt.`, {
					action: {
						label: "Lieferschein erstellen",
						onClick: () => {
							// Öffne das NewLieferscheinDialog mit vorausgefüllten Daten
							navigate(
								`/erstellung/lieferscheine?kundenId=${selectedKundeId}&leistungIds=${results.join(",")}&openDialog=true`,
							);
						},
					},
					duration: 8000, // 8 Sekunden anzeigen
				});
			} else {
				toast.success(
					`${results.length} Leistung(en) erfolgreich aktualisiert.`,
				);
			}

			onSubmitSuccess();
		} catch (error: any) {
			toast.error(`Fehler beim Speichern: ${error.message}`);
		}
	};

	const customFooter = (
		<div className="flex justify-between items-center w-full">
			<div className="flex items-center gap-4 text-sm text-gray-300">
				<span>
					Dauer:{" "}
					<span className="font-medium text-white">
						{formatHours(totalSummary.stunden)}
					</span>
				</span>
				<span>
					Gesamtkosten:{" "}
					<span className="font-medium text-white">
						{formatCurrency(totalSummary.gesamt)}
					</span>
				</span>
			</div>
			<div className="flex gap-2">
				<Button
					variant="outline"
					onClick={onCancel}
					size="sm"
					className="h-8 text-xs"
				>
					Abbrechen
				</Button>
				<Button onClick={handleSubmit} size="sm" className="gap-1 h-8 text-xs">
					<Check className="h-4 w-4" />
					{isEditing ? "Änderungen speichern" : "Leistungen speichern"}
				</Button>
			</div>
		</div>
	);

	return (
		<AppDialogLayout
			isOpen={true}
			onClose={onCancel}
			title={isEditing ? "Leistung bearbeiten" : "Neue Leistungen erfassen"}
			icon={isEditing ? <Pencil /> : <PlusCircle />}
			footer={customFooter}
			maxWidth="xl"
		>
			<div className="space-y-1">
				{/* Globale Kundenauswahl */}
				{!isEditing && (
					<div className="flex items-center gap-2 mb-2">
						<Label
							htmlFor="globalKundeId"
							className="text-xs font-medium text-gray-300 whitespace-nowrap min-w-[50px]"
						>
							Kunde:
						</Label>
						<Select value={selectedKundeId} onValueChange={handleKundeChange}>
							<SelectTrigger
								id="globalKundeId"
								className="h-7 flex-1 bg-gray-700 border-gray-600 text-gray-200 text-sm"
							>
								<SelectValue placeholder="Kunde auswählen..." />
							</SelectTrigger>
							<SelectContent>
								{kunden.map((k) => (
									<SelectItem key={k._id} value={k._id}>
										{k.name}
									</SelectItem>
								))}
							</SelectContent>
						</Select>
						{selectedKundeId && (
							<Button
								variant="outline"
								size="icon"
								onClick={handleAddItem}
								className="h-7 w-7 bg-gray-800 border-gray-600 text-gray-200 hover:bg-gray-700 shrink-0"
								title="Position hinzufügen"
							>
								<Plus className="h-3 w-3" />
							</Button>
						)}
					</div>
				)}

				<div className="space-y-1 max-h-[60vh] overflow-y-auto pr-1">
					{formState.map((item, index) => (
						<LeistungItemForm
							key={item.id}
							item={item}
							index={index}
							onFormChange={handleFormChange}
							onRemove={handleRemoveItem}
							kunden={kunden}
							mitarbeiter={mitarbeiter}
							isOnlyItem={formState.length === 1}
							isEditing={isEditing}
							selectedKundeId={selectedKundeId}
							showKundeSelection={isEditing}
						/>
					))}
				</div>
			</div>
		</AppDialogLayout>
	);
}

// Helper to get timestamps for a single item
const getTimestamps = (
	datum: string,
	startUhrzeit: string,
	endUhrzeit: string,
) => {
	try {
		const [year, month, day] = datum.split("-").map(Number);
		const [startHour, startMinute] = startUhrzeit.split(":").map(Number);
		const [endHour, endMinute] = endUhrzeit.split(":").map(Number);
		const isDST = (m: number) => m >= 3 && m <= 10;
		const offsetHours = isDST(month) ? 2 : 1;
		const startDate = new Date(
			Date.UTC(year, month - 1, day, startHour - offsetHours, startMinute, 0),
		);
		let endDate = new Date(
			Date.UTC(year, month - 1, day, endHour - offsetHours, endMinute, 0),
		);
		if (endDate <= startDate) {
			endDate = new Date(
				Date.UTC(year, month - 1, day + 1, endHour - offsetHours, endMinute, 0),
			);
		}
		return { startZeit: startDate.getTime(), endZeit: endDate.getTime() };
	} catch (e) {
		return null;
	}
};

interface LeistungItemFormProps {
	item: LeistungFormData;
	index: number;
	onFormChange: (
		id: string,
		field: keyof LeistungFormData,
		value: string,
	) => void;
	onRemove: (id: string) => void;
	kunden: KundeDoc[];
	mitarbeiter: MitarbeiterDoc[];
	isOnlyItem: boolean;
	isEditing: boolean;
	selectedKundeId: string;
	showKundeSelection: boolean;
}

function LeistungItemForm({
	item,
	index,
	onFormChange,
	onRemove,
	kunden,
	mitarbeiter,
	isOnlyItem,
	isEditing,
	selectedKundeId,
	showKundeSelection,
}: LeistungItemFormProps) {
	const [showSecondKontingent, setShowSecondKontingent] = useState(false);

	const aktiveKontingente = useQuery(
		api.verwaltung.kontingente.getActiveByKunde,
		selectedKundeId ? { kundenId: selectedKundeId as Id<"kunden"> } : "skip",
	) as KontingentOption[] | undefined;

	const handleChange = (field: keyof LeistungFormData, value: string) => {
		onFormChange(item.id, field, value);
	};

	const summaryData = useMemo(() => {
		const timestamps = getTimestamps(
			item.datum,
			item.startUhrzeit,
			item.endUhrzeit,
		);
		if (!timestamps) return { stunden: 0, gesamt: 0 };
		const stunden = calculateHours(timestamps.startZeit, timestamps.endZeit);
		const preis = Number.parseFloat(item.stundenpreisInput) || 0;
		const anfahrt =
			item.art === "vor-Ort"
				? Number.parseFloat(item.anfahrtskostenInput) || 0
				: 0;
		return { stunden, gesamt: stunden * preis + anfahrt };
	}, [
		item.datum,
		item.startUhrzeit,
		item.endUhrzeit,
		item.stundenpreisInput,
		item.anfahrtskostenInput,
		item.art,
	]);

	const selectedKontingent1 = useMemo(() => {
		return aktiveKontingente?.find((k) => k._id === item.kontingentId);
	}, [aktiveKontingente, item.kontingentId]);

	const verfuegbareStunden1 = useMemo(() => {
		if (!selectedKontingent1) return 0;
		return selectedKontingent1.stunden - selectedKontingent1.verbrauchteStunden;
	}, [selectedKontingent1]);

	useEffect(() => {
		if (item.kontingentId && summaryData.stunden > verfuegbareStunden1) {
			setShowSecondKontingent(true);
		} else {
			setShowSecondKontingent(false);
			handleChange("kontingentId2", "");
		}
	}, [summaryData.stunden, verfuegbareStunden1, item.kontingentId]);

	return (
		<div className="bg-gray-800/50 border border-gray-700 rounded-lg p-2 space-y-2 relative">
			{/* Main Layout: Einstellungen links, Beschreibung rechts */}
			<div className="grid grid-cols-12 gap-3">
				{/* Linke Seite: Einstellungen */}
				<div className="col-span-7 space-y-2">
					{/* Row 1: Mitarbeiter, Art, Datum, Zeit */}
					<div className="grid grid-cols-12 gap-2">
						<div className="col-span-4">
							<Label
								htmlFor={`mitarbeiterId-${item.id}`}
								className="text-xs font-medium text-gray-400"
							>
								Mitarbeiter
							</Label>
							<Select
								value={item.mitarbeiterId}
								onValueChange={(value) => handleChange("mitarbeiterId", value)}
							>
								<SelectTrigger
									id={`mitarbeiterId-${item.id}`}
									className="h-7 text-xs bg-gray-700 border-gray-600 text-gray-200"
								>
									<SelectValue placeholder="Mitarbeiter..." />
								</SelectTrigger>
								<SelectContent>
									{mitarbeiter.map((m) => (
										<SelectItem key={m._id} value={m._id}>
											{m.name}
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						<div className="col-span-4">
							<Label
								htmlFor={`art-${item.id}`}
								className="text-xs font-medium text-gray-400"
							>
								Leistung
							</Label>
							<Select
								value={item.art}
								onValueChange={(value) => handleChange("art", value)}
							>
								<SelectTrigger
									id={`art-${item.id}`}
									className="h-7 text-xs bg-gray-700 border-gray-600 text-gray-200"
								>
									<SelectValue />
								</SelectTrigger>
								<SelectContent>
									<SelectItem value="remote">Remote</SelectItem>
									<SelectItem value="vor-Ort">Vor-Ort</SelectItem>
									<SelectItem value="vor-Ort (free)">Vor-Ort (frei)</SelectItem>
								</SelectContent>
							</Select>
						</div>

						<div className="col-span-4">
							<Label
								htmlFor={`datum-${item.id}`}
								className="text-xs font-medium text-gray-400"
							>
								Datum
							</Label>
							<Input
								id={`datum-${item.id}`}
								type="date"
								value={item.datum}
								onChange={(e) => handleChange("datum", e.target.value)}
								className="h-7 text-xs bg-gray-700 border-gray-600 text-gray-200"
							/>
						</div>
					</div>

					{/* Row 2: Zeit */}
					<div className="grid grid-cols-12 gap-2">
						<div className="col-span-6">
							<Label
								htmlFor={`startUhrzeit-${item.id}`}
								className="text-xs font-medium text-gray-400"
							>
								Von
							</Label>
							<Input
								id={`startUhrzeit-${item.id}`}
								type="time"
								step="900"
								value={item.startUhrzeit}
								onChange={(e) => handleChange("startUhrzeit", e.target.value)}
								className="h-7 text-xs bg-gray-700 border-gray-600 text-gray-200"
							/>
						</div>

						<div className="col-span-6">
							<Label
								htmlFor={`endUhrzeit-${item.id}`}
								className="text-xs font-medium text-gray-400"
							>
								Bis
							</Label>
							<Input
								id={`endUhrzeit-${item.id}`}
								type="time"
								step="900"
								value={item.endUhrzeit}
								onChange={(e) => handleChange("endUhrzeit", e.target.value)}
								className="h-7 text-xs bg-gray-700 border-gray-600 text-gray-200"
							/>
						</div>
					</div>

					{/* Row 3: Kontingente */}
					<div className="grid grid-cols-12 gap-2">
						<div
							className={showSecondKontingent ? "col-span-6" : "col-span-12"}
						>
							<Label
								htmlFor={`kontingentId-${item.id}`}
								className="text-xs font-medium text-gray-400"
							>
								Kontingent
							</Label>
							<Select
								value={item.kontingentId}
								onValueChange={(value) => handleChange("kontingentId", value)}
								disabled={!selectedKundeId || !aktiveKontingente}
							>
								<SelectTrigger
									id={`kontingentId-${item.id}`}
									className="h-7 text-xs bg-gray-700 border-gray-600 text-gray-200"
								>
									<SelectValue placeholder="Kontingent auswählen..." />
								</SelectTrigger>
								<SelectContent>
									{aktiveKontingente?.map((k) => (
										<SelectItem key={k._id} value={k._id}>
											{k.name} ({formatHours(k.stunden - k.verbrauchteStunden)}{" "}
											verfügbar)
										</SelectItem>
									))}
								</SelectContent>
							</Select>
						</div>

						{showSecondKontingent && (
							<div className="col-span-6">
								<Label
									htmlFor={`kontingentId2-${item.id}`}
									className="text-xs font-medium text-gray-400"
								>
									Zweites Kontingent
								</Label>
								<Select
									value={item.kontingentId2}
									onValueChange={(value) =>
										handleChange("kontingentId2", value)
									}
									disabled={!item.kontingentId}
								>
									<SelectTrigger
										id={`kontingentId2-${item.id}`}
										className="h-7 text-xs bg-gray-700 border-gray-600 text-gray-200"
									>
										<SelectValue placeholder="Zweites Kontingent..." />
									</SelectTrigger>
									<SelectContent>
										{aktiveKontingente
											?.filter((k) => k._id !== item.kontingentId)
											.map((k) => (
												<SelectItem key={k._id} value={k._id}>
													{k.name} (
													{formatHours(k.stunden - k.verbrauchteStunden)}{" "}
													verfügbar)
												</SelectItem>
											))}
									</SelectContent>
								</Select>
							</div>
						)}
					</div>
				</div>

				{/* Rechte Seite: Beschreibung */}
				<div className="col-span-5 flex flex-col">
					<Label
						htmlFor={`beschreibung-${item.id}`}
						className="text-xs font-medium text-gray-400 mb-2"
					>
						Beschreibung
					</Label>
					<Textarea
						id={`beschreibung-${item.id}`}
						value={item.beschreibung}
						onChange={(e) => handleChange("beschreibung", e.target.value)}
						className="flex-1 text-xs bg-gray-700 border-gray-600 text-gray-200 resize-none"
						placeholder="Beschreibung der erbrachten Leistung..."
					/>
				</div>
			</div>

			{/* Validation & Warnings */}
			{item.beschreibung.length > 0 && item.beschreibung.length < 3 && (
				<div className="p-2 bg-red-900/50 border border-red-700/60 rounded text-xs text-red-300 flex items-center gap-2">
					<AlertCircle className="h-4 w-4" />
					Die Beschreibung muss mindestens drei Zeichen enthalten.
				</div>
			)}

			{/* Footer: Summary & Actions */}
			<div className="mt-2 pt-2 border-t border-gray-700 flex justify-between items-center text-xs">
				<div className="flex items-center gap-2">
					{!isOnlyItem && !isEditing && (
						<Button
							variant="ghost"
							size="icon"
							onClick={() => onRemove(item.id)}
							className="h-6 w-6 text-gray-500 hover:text-red-400"
						>
							<Trash2 className="h-4 w-4" />
						</Button>
					)}
					<div className="flex items-center gap-4 text-gray-300">
						<span>
							Dauer:{" "}
							<span className="font-medium text-white">
								{formatHours(summaryData.stunden)}
							</span>
						</span>
						<span>
							Gesamtkosten:{" "}
							<span className="font-medium text-white">
								{formatCurrency(summaryData.gesamt)}
							</span>
						</span>
						{showSecondKontingent && (
							<div
								className="inline-flex items-center justify-center w-4 h-4 rounded-full bg-yellow-900/50 border border-yellow-700/60 text-yellow-300 cursor-help"
								title="Leistung wird auf zwei Kontingente aufgeteilt"
							>
								<Split className="h-2.5 w-2.5" />
							</div>
						)}
					</div>
				</div>

				<div className="flex items-center gap-2">
					<span className="text-gray-400">Stundenpreis:</span>
					<Input
						id={`stundenpreisInput-${item.id}`}
						type="number"
						value={item.stundenpreisInput}
						onChange={(e) => handleChange("stundenpreisInput", e.target.value)}
						className="h-6 text-xs bg-gray-700 border-gray-600 text-gray-200 w-20"
						placeholder="€/Std"
					/>
					<span className="text-gray-400">Anfahrt:</span>
					<Input
						id={`anfahrtskostenInput-${item.id}`}
						type="number"
						value={item.art === "remote" ? "0" : item.anfahrtskostenInput}
						onChange={(e) =>
							handleChange("anfahrtskostenInput", e.target.value)
						}
						disabled={item.art === "remote"}
						className={`h-6 text-xs w-20 ${
							item.art === "remote"
								? "bg-gray-800 border-gray-700 text-gray-500 cursor-not-allowed"
								: "bg-gray-700 border-gray-600 text-gray-200"
						}`}
						placeholder="€"
					/>
				</div>
			</div>
		</div>
	);
}
