import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { Button } from "@/components/_shared/Button";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { AppDialogLayout } from "@/components/layout/AppDialogLayout";
import { useMutation, useQuery } from "convex/react";
import { Plus, User } from "lucide-react";
import { useMemo, useState, useEffect } from "react";
import { toast } from "sonner";
import { LeistungList } from "./LeistungList";

interface NewLieferscheinDialogProps {
	isOpen: boolean;
	onClose: () => void;
	prefilledKundeId?: Id<"kunden"> | null;
	prefilledLeistungIds?: Id<"kunden_leistungen">[];
}

export function NewLieferscheinDialog({
	isOpen,
	onClose,
	prefilledKundeId,
	prefilledLeistungIds = [],
}: NewLieferscheinDialogProps) {
	const [kundeId, setKundeId] = useState<Id<"kunden"> | "">("");
	const [selectedLeistungen, setSelectedLeistungen] = useState<
		Id<"kunden_leistungen">[]
	>([]);

	const kunden = useQuery(api.verwaltung.kunden.list) || [];
	const allLeistungen = useQuery(api.erstellung.leistung.list) || [];
	const createLieferschein = useMutation(api.erstellung.lieferschein.create);

	// Vorausgefüllte Daten setzen, wenn Dialog geöffnet wird
	useEffect(() => {
		if (isOpen && prefilledKundeId) {
			setKundeId(prefilledKundeId);
			setSelectedLeistungen(prefilledLeistungIds);
		}
	}, [isOpen, prefilledKundeId, prefilledLeistungIds]);

	// Filter leistungen by selected kunde
	const kundenLeistungen = useMemo(() => {
		if (!kundeId) return [];
		return allLeistungen.filter((l) => l.kundenId === kundeId);
	}, [allLeistungen, kundeId]);

	const handleSubmit = async () => {
		if (!kundeId) {
			toast.error("Bitte wählen Sie einen Kunden aus.");
			return;
		}

		try {
			const lieferscheinId = await createLieferschein({
				kundenId: kundeId,
				leistungIds: selectedLeistungen,
			});

			toast.success("Lieferschein-Entwurf erfolgreich erstellt.");
			resetForm();
			onClose();

			// Zur Detailseite navigieren
			if (lieferscheinId) {
				window.location.href = `/erstellung/lieferscheine/${lieferscheinId}`;
			}
		} catch (error) {
			toast.error("Fehler beim Erstellen des Lieferscheins.");
		}
	};

	const resetForm = () => {
		setKundeId("");
		setSelectedLeistungen([]);
	};

	const toggleLeistung = (leistungId: Id<"kunden_leistungen">) => {
		setSelectedLeistungen((prev) =>
			prev.includes(leistungId)
				? prev.filter((id) => id !== leistungId)
				: [...prev, leistungId],
		);
	};

	const toggleAllLeistungen = () => {
		if (selectedLeistungen.length === kundenLeistungen.length) {
			setSelectedLeistungen([]);
		} else {
			setSelectedLeistungen(kundenLeistungen.map((l) => l._id));
		}
	};

	return (
		<AppDialogLayout
			isOpen={isOpen}
			onClose={onClose}
			title="Neuen Lieferschein erstellen"
			maxWidth="lg"
			footerAction={{
				label:
					selectedLeistungen.length > 0
						? `Erstellen (${selectedLeistungen.length} Leistung${selectedLeistungen.length !== 1 ? "en" : ""})`
						: "Leeren Entwurf erstellen",
				onClick: handleSubmit,
				icon: <Plus className="h-3.5 w-3.5" />,
				disabled: !kundeId,
			}}
		>
			<div className="space-y-4">
				<div className="space-y-2">
					<label
						htmlFor="kundeId"
						className="text-sm font-medium text-gray-300 flex items-center gap-2"
					>
						<User className="h-4 w-4 text-gray-400" />
						Kunde
					</label>
					<Select
						value={kundeId}
						onValueChange={(value: string) => {
							setKundeId(value as Id<"kunden">);
							// Nur Leistungen zurücksetzen, wenn es keine vorausgefüllten Daten gibt
							if (!prefilledKundeId) {
								setSelectedLeistungen([]);
							}
						}}
					>
						<SelectTrigger id="kundeId" className="w-full">
							<SelectValue placeholder="Kunde auswählen" />
						</SelectTrigger>
						<SelectContent>
							{kunden.map((kunde) => (
								<SelectItem key={kunde._id} value={kunde._id}>
									{kunde.name}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				</div>

				{kundeId && kundenLeistungen.length > 0 && (
					<LeistungList
						leistungen={kundenLeistungen}
						selectedIds={selectedLeistungen}
						onToggleLeistung={toggleLeistung}
						onToggleAll={toggleAllLeistungen}
						emptyMessage="Keine Leistungen für diesen Kunden vorhanden."
					/>
				)}
			</div>
		</AppDialogLayout>
	);
}
