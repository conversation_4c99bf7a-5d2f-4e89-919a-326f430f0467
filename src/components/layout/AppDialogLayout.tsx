import { Button } from "@/components/_shared/Button";
import {
	Dialog,
	DialogContent,
	DialogDescription,
	DialogHeader,
	DialogTitle,
} from "@/components/_shared/Dialog";
import { ReactNode } from "react";

interface AppDialogLayoutProps {
	isOpen: boolean;
	onClose: () => void;
	title: string;
	description?: string;
	icon?: ReactNode;
	children: ReactNode;
	footer?: ReactNode;
	footerAction?: {
		label: string;
		onClick: () => void;
		icon?: ReactNode;
		disabled?: boolean;
		loading?: boolean; // Added from LeistungDialogLayout
	};
	maxWidth?: "sm" | "md" | "lg" | "xl" | "2xl" | "3xl" | "4xl" | "5xl";
}

export function AppDialogLayout({
	isOpen,
	onClose,
	title,
	description,
	icon,
	children,
	footer,
	footerAction,
	maxWidth = "md",
}: AppDialogLayoutProps) {
	const maxWidthClasses = {
		sm: "sm:max-w-[500px]",
		md: "sm:max-w-[600px]",
		lg: "sm:max-w-[700px]",
		xl: "sm:max-w-[800px]",
		"2xl": "sm:max-w-[900px]",
		"3xl": "sm:max-w-[1000px]",
		"4xl": "sm:max-w-[1200px]",
		"5xl": "sm:max-w-[1400px]",
	};

	return (
		<Dialog open={isOpen} onOpenChange={(open) => !open && onClose()}>
			<DialogContent
				className={`${maxWidthClasses[maxWidth]} p-0 overflow-hidden`}
			>
				<DialogHeader className="p-4 pb-1">
					<DialogTitle className="text-base flex items-center gap-1.5">
						{icon}
						{title}
					</DialogTitle>
					{description && (
						<DialogDescription className="text-xs">
							{description}
						</DialogDescription>
					)}
				</DialogHeader>

				<div className="px-4 py-2">{children}</div>

				<div className="flex justify-between items-center p-3 bg-gray-900 border-t border-gray-800 gap-2">
					{footer ? (
						footer
					) : (
						<>
							<div></div>
							<div className="flex gap-2">
								<Button
									variant="outline"
									onClick={onClose}
									size="sm"
									className="h-8 text-xs"
								>
									Abbrechen
								</Button>
								{footerAction && (
									<Button
										onClick={footerAction.onClick}
										disabled={footerAction.disabled || footerAction.loading}
										size="sm"
										className="gap-1 h-8 text-xs"
									>
										{footerAction.loading ? (
											"Wird verarbeitet..."
										) : (
											<>
												{footerAction.icon}
												{footerAction.label}
											</>
										)}
									</Button>
								)}
							</div>
						</>
					)}
				</div>
			</DialogContent>
		</Dialog>
	);
}
