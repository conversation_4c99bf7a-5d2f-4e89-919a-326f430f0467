import { api } from "@/../convex/_generated/api";
import { Id } from "@/../convex/_generated/dataModel";
import { PDFLieferscheinDocument } from "@/components/erstellung/lieferscheine/PDFDocument";
import { formatCurrency, formatHours } from "@/lib/utils/formatUtils";
import { pdf } from "@react-pdf/renderer";
import { useMutation, useQuery } from "convex/react";
import { Building, CheckCircle, Mail, User } from "lucide-react";
import { useEffect, useState } from "react";
import { toast } from "sonner";
import { defaultLieferscheinConfig } from "@/../convex/erstellung/lieferscheineConfig";

interface EmailDialogProps {
	isOpen: boolean;
	onClose: () => void;
	type: "lieferschein" | "uebersicht";
	lieferscheinId?: Id<"kunden_lieferscheine">;
	kundeId?: Id<"kunden">;
	zeitraum?: string;
	pdfBase64?: string;
}

export function EmailDialog({
	isOpen,
	onClose,
	type,
	lieferscheinId,
	kundeId,
	zeitraum,
	pdfBase64,
}: EmailDialogProps) {
	const [isSubmitting, setIsSubmitting] = useState(false);
	const [selectedEmployees, setSelectedEmployees] = useState<string[]>([]);
	const [selectedCustomers, setSelectedCustomers] = useState<string[]>([]);
	const [hasInitialized, setHasInitialized] = useState(false);

	// Get recipient info - always call hooks, use skip parameter
	const lieferscheinDetails = useQuery(
		api.erstellung.lieferschein.get,
		lieferscheinId ? { id: lieferscheinId } : "skip",
	);

	const kundeIdToUse = kundeId || lieferscheinDetails?.lieferschein?.kundenId;
	const kunde = useQuery(
		api.verwaltung.kunden.get,
		kundeIdToUse ? { id: kundeIdToUse } : "skip",
	);

	// Diese useState-Zeilen sind fehlerhaft - entfernt

	// Send email mutations
	const sendLieferschein = useMutation(api.system.email.sendLieferschein);
	const sendUebersicht = useMutation(api.system.email.sendUebersicht);

	// Function to get main contact for email greeting (istEmailAnrede)
	const getEmailGreetingContact = () => {
		if (!kunde?.ansprechpartner) return null;
		// Only return contact with istEmailAnrede = true
		return kunde.ansprechpartner.find((a) => a.istEmailAnrede) || null;
	};

	// Function to get email recipients for customers based on type
	const getCustomerEmailRecipients = () => {
		if (!kunde?.ansprechpartner) return [];
		if (type === "lieferschein") {
			return kunde.ansprechpartner.filter(
				(a) => a.istEmailLieferscheinEmpfaenger && a.email,
			);
		} else if (type === "uebersicht") {
			return kunde.ansprechpartner.filter(
				(a) => a.istEmailUebersichtEmpfaenger && a.email,
			);
		}
		return [];
	};

	// Get employee IDs from Lieferschein or Übersicht
	const employeeIds =
		type === "lieferschein" && lieferscheinDetails?.leistungen
			? Array.from(
					new Set(
						lieferscheinDetails.leistungen
							.map((l) => l.mitarbeiterId)
							.filter(Boolean),
					),
				)
			: [];

	// Query all employees
	const allEmployees = useQuery(api.verwaltung.mitarbeiter.list);

	// Function to get employees involved in services (default selected)
	const getInvolvedEmployees = () => {
		if (!allEmployees) return [];
		if (type === "lieferschein" && employeeIds.length > 0) {
			return allEmployees.filter((emp) => employeeIds.includes(emp._id));
		}
		// For Übersicht, default to all employees
		return allEmployees;
	};

	// Function to generate PDF for Lieferschein
	const generateLieferscheinPDF = async () => {
		if (!lieferscheinDetails?.lieferschein || !lieferscheinDetails.leistungen) {
			throw new Error("Lieferschein-Daten nicht verfügbar");
		}

		const { lieferschein, leistungen } = lieferscheinDetails;
		const settings = defaultLieferscheinConfig.settings;

		const pdfDocument = (
			<PDFLieferscheinDocument
				lieferschein={{
					_id: lieferschein._id,
					nummer: lieferschein.nummer || "",
					erstelltAm: lieferschein.erstelltAm,
					erstelltAmFormatiert: new Date(
						lieferschein.erstelltAm,
					).toLocaleDateString("de-DE"),
					istKorrektur: lieferschein.istKorrektur,
					bemerkung: lieferschein.bemerkung,
					kundeName: kunde?.name || "",
				}}
				leistungen={leistungen.map((l: any) => ({
					_id: l._id,
					startZeit: l.startZeit,
					endZeit: l.endZeit,
					art: l.art,
					beschreibung: l.beschreibung,
					stunden: l.stunden,
					stundenpreis: l.stundenpreis,
					anfahrtskosten: l.anfahrtskosten,
					mitAnfahrt: l.mitAnfahrt,
					datum: new Date(l.startZeit).toLocaleDateString("de-DE"),
					startZeitFormatiert: new Date(l.startZeit).toLocaleTimeString(
						"de-DE",
						{ hour: "2-digit", minute: "2-digit" },
					),
					endZeitFormatiert: new Date(l.endZeit).toLocaleTimeString("de-DE", {
						hour: "2-digit",
						minute: "2-digit",
					}),
					kontingentName: l.kontingentName,
					kontingentName2: l.kontingentName2,
					mitarbeiterName: l.mitarbeiterName,
				}))}
				includeHeader={settings.includeHeader}
				includeFooter={settings.includeFooter}
				showLogo={settings.showLogo}
				includeLegalText={!!settings.legalText}
				includeSignatureField={settings.includeSignatureField}
				logoUrl={settings.logoPath}
				firmenName="innov8-IT"
				firmenFusszeileText={settings.fusszeileText}
				legalText={settings.legalText}
				signatureText={settings.signatureText}
				formatCurrency={formatCurrency}
				formatHours={formatHours}
			/>
		);

		const blob = await pdf(pdfDocument).toBlob();
		const buffer = await blob.arrayBuffer();
		const base64 = btoa(String.fromCharCode(...new Uint8Array(buffer)));
		return base64;
	};

	const handleSubmit = async () => {
		if (!isOpen) return;

		setIsSubmitting(true);

		try {
			if (type === "lieferschein" && lieferscheinId) {
				try {
					// Generate PDF first
					const pdfBase64 = await generateLieferscheinPDF();

					// Prepare recipients
					const employeeRecipients =
						selectedEmployees.length > 0
							? allEmployees
									?.filter((emp) => selectedEmployees.includes(emp._id))
									.map((emp) => ({
										email: emp.email,
										name: emp.name,
									})) || []
							: [];

					const customerRecipients =
						selectedCustomers.length > 0
							? kunde?.ansprechpartner
									?.filter((a) => selectedCustomers.includes(a.email || ""))
									.map((a) => ({
										email: a.email || "",
										name: a.name,
									})) || []
							: [];

					// Then send email with PDF attached
					await sendLieferschein({
						lieferscheinId,
						sendToMitarbeiter: employeeRecipients.length > 0,
						sendToKunde: customerRecipients.length > 0,
						pdfBase64,
						mainContactName:
							emailGreetingContact?.name ||
							kunde?.ansprechpartner?.find((a) => a.istHauptansprechpartner)
								?.name ||
							"Kunde",
						employeeRecipients,
						customerRecipients,
					});

					toast.success("E-Mail wurde erfolgreich versendet!");
				} catch (pdfError: any) {
					toast.error(
						`Fehler bei PDF-Generierung: ${pdfError.message || "Unbekannter Fehler"}`,
					);
					return;
				}
			} else if (type === "uebersicht" && kundeId && zeitraum && pdfBase64) {
				// Prepare recipients
				const employeeRecipients =
					selectedEmployees.length > 0
						? allEmployees
								?.filter((emp) => selectedEmployees.includes(emp._id))
								.map((emp) => ({
									email: emp.email,
									name: emp.name,
								})) || []
						: [];

				const customerRecipients =
					selectedCustomers.length > 0
						? kunde?.ansprechpartner
								?.filter((a) => selectedCustomers.includes(a.email || ""))
								.map((a) => ({
									email: a.email || "",
									name: a.name,
								})) || []
						: [];

				await sendUebersicht({
					kundeId,
					zeitraum,
					pdfBase64,
					employeeRecipients,
					customerRecipients,
					mainContactName:
						emailGreetingContact?.name ||
						kunde?.ansprechpartner?.find((a) => a.istHauptansprechpartner)
							?.name ||
						"Kunde",
				});

				toast.success("E-Mail wurde erfolgreich versendet!");
			} else {
				throw new Error("Ungültige Parameter für E-Mail-Versand.");
			}

			onClose();
		} catch (error: any) {
			toast.error(
				`Fehler beim Versenden der E-Mail: ${error.message || "Unbekannter Fehler"}`,
			);
		} finally {
			setIsSubmitting(false);
		}
	};

	// Get recipient details
	const emailGreetingContact = getEmailGreetingContact();
	const customerRecipients = getCustomerEmailRecipients();
	// Show all customer contacts with email addresses in CC list
	const allCustomerContacts =
		kunde?.ansprechpartner?.filter((a) => a.email) || [];
	const involvedEmployees = getInvolvedEmployees();
	// otherEmployees not needed anymore since we show all employees in one list

	// Initialize selected recipients when data loads (only once)
	useEffect(() => {
		if (!hasInitialized && isOpen) {
			if (type === "lieferschein") {
				// For Lieferschein: Select involved employees by default
				if (involvedEmployees.length > 0) {
					setSelectedEmployees(involvedEmployees.map((e) => e._id));
				}
				// Select customers marked as email recipients by default
				if (customerRecipients.length > 0) {
					setSelectedCustomers(customerRecipients.map((c) => c.email || ""));
				}
			} else if (type === "uebersicht") {
				// For Übersicht: Select ALL employees by default
				if (allEmployees && allEmployees.length > 0) {
					setSelectedEmployees(allEmployees.map((e) => e._id));
				}
				// For Übersicht: Select customers marked as Übersicht recipients by default
				if (customerRecipients.length > 0) {
					setSelectedCustomers(customerRecipients.map((c) => c.email || ""));
				}
			}
			setHasInitialized(true);
		}
	}, [
		type,
		involvedEmployees,
		customerRecipients,
		allEmployees,
		isOpen,
		hasInitialized,
	]);

	// Reset initialization when dialog closes
	useEffect(() => {
		if (!isOpen) {
			setHasInitialized(false);
			setSelectedEmployees([]);
			setSelectedCustomers([]);
		}
	}, [isOpen]);

	// Check if there are any email recipients
	const hasEmailRecipients = allCustomerContacts.length > 0;
	const hasEmployeeRecipients = allEmployees && allEmployees.length > 0;

	if (!isOpen) return null;

	return (
		<div
			className="fixed inset-0 z-[9999] flex items-center justify-center bg-black/70 p-4 overflow-y-auto"
			style={{ position: "fixed", top: 0, left: 0, right: 0, bottom: 0 }}
		>
			<div className="w-full max-w-4xl rounded-lg bg-gray-800 shadow-xl border border-gray-700 animate-in fade-in zoom-in-95 duration-200 my-auto">
				<div className="flex items-center justify-between p-4 border-b border-gray-700">
					<h3 className="text-lg font-medium text-white flex items-center">
						<Mail className="mr-2 h-5 w-5 text-blue-400" />
						{type === "lieferschein"
							? "Lieferschein per E-Mail versenden"
							: "Übersicht per E-Mail versenden"}
					</h3>
					<button
						onClick={onClose}
						className="rounded-full p-1 text-gray-400 hover:bg-gray-700 hover:text-white"
					>
						<span className="sr-only">Schließen</span>
						<svg
							className="h-5 w-5"
							fill="none"
							viewBox="0 0 24 24"
							stroke="currentColor"
						>
							<path
								strokeLinecap="round"
								strokeLinejoin="round"
								strokeWidth={2}
								d="M6 18L18 6M6 6l12 12"
							/>
						</svg>
					</button>
				</div>

				<div className="p-4">
					<div className="space-y-4">
						{/* E-Mail Anrede (Blue) - Top Row */}
						<div>
							<h4 className="text-base font-medium text-white mb-2">
								E-Mail Anrede
							</h4>

							<div className="bg-blue-900/30 border border-blue-700/50 p-3 rounded-md">
								<div className="text-sm font-medium text-blue-300 mb-2 flex items-center">
									<Mail className="h-4 w-4 mr-2" />
									Hauptempfänger (Anrede)
								</div>
								{emailGreetingContact ? (
									<>
										<div className="text-white font-medium">
											{emailGreetingContact.name}
										</div>
										<div className="text-gray-300 text-sm">
											{emailGreetingContact.email || "Keine E-Mail-Adresse"}
										</div>
										<div className="text-xs text-blue-300 mt-1">
											Anrede: "Sehr geehrte(r) {emailGreetingContact.name}"
										</div>
										<div className="text-xs text-green-400 mt-1">
											✓ Als E-Mail-Anrede definiert
										</div>
									</>
								) : (
									<>
										<div className="text-red-300 font-medium">
											Keine E-Mail-Anrede definiert
										</div>
										<div className="text-gray-400 text-sm">
											Bitte in Kundendaten einen Ansprechpartner als
											E-Mail-Anrede markieren
										</div>
										<div className="text-xs text-red-400 mt-1">
											❌ E-Mail kann nicht versendet werden
										</div>
									</>
								)}
							</div>
						</div>

						{/* Bottom Row: Mitarbeiter (Green) and Kunden (Yellow) Side by Side */}
						<div className="grid grid-cols-1 lg:grid-cols-2 gap-4">
							{/* Mitarbeiter (Green) - Left */}
							<div>
								<h4 className="text-base font-medium text-white mb-2">
									Mitarbeiter
								</h4>

								{hasEmployeeRecipients && (
									<div className="bg-green-900/30 border border-green-700/50 p-3 rounded-md">
										<div className="font-medium text-green-300 mb-2 flex items-center">
											<User className="h-4 w-4 mr-2" />
											{type === "lieferschein"
												? "Mitarbeiter in CC"
												: "Alle Mitarbeiter in CC"}
										</div>
										<div className="space-y-1.5 max-h-36 overflow-y-auto">
											{allEmployees?.map((employee) => {
												const isInvolved = involvedEmployees.some(
													(inv) => inv._id === employee._id,
												);
												return (
													<div
														key={employee._id}
														className="flex items-center space-x-2"
													>
														<input
															type="checkbox"
															id={`employee-${employee._id}`}
															checked={selectedEmployees.includes(employee._id)}
															onChange={(e) => {
																if (e.target.checked) {
																	setSelectedEmployees((prev) => [
																		...prev,
																		employee._id,
																	]);
																} else {
																	setSelectedEmployees((prev) =>
																		prev.filter((id) => id !== employee._id),
																	);
																}
															}}
															className="h-4 w-4 text-green-500 rounded border-gray-600 focus:ring-green-500 focus:ring-opacity-25 bg-gray-700"
														/>
														<label
															htmlFor={`employee-${employee._id}`}
															className="text-sm text-gray-300 flex-1"
														>
															<div className="font-medium flex items-center gap-2">
																{employee.name}
																{isInvolved && (
																	<span className="text-xs text-green-400 bg-green-900/50 px-1.5 py-0.5 rounded">
																		{type === "lieferschein"
																			? "beteiligt"
																			: "standard"}
																	</span>
																)}
															</div>
															<div className="text-xs text-gray-400">
																{employee.email}
															</div>
														</label>
													</div>
												);
											})}
										</div>
									</div>
								)}

								{!hasEmployeeRecipients && (
									<div className="bg-amber-900/30 border border-amber-700/50 p-3 rounded-md">
										<p className="text-amber-300 text-sm">
											⚠️ Keine Mitarbeiter verfügbar.
										</p>
									</div>
								)}
							</div>

							{/* Kunden Ansprechpartner (Yellow) - Right */}
							<div>
								<h4 className="text-base font-medium text-white mb-2">
									Kunden-Ansprechpartner
								</h4>

								{hasEmailRecipients && (
									<div className="bg-yellow-900/30 border border-yellow-700/50 p-3 rounded-md">
										<div className="font-medium text-yellow-300 mb-2 flex items-center">
											<Building className="h-4 w-4 mr-2" />
											Weitere Ansprechpartner in CC
										</div>
										<div className="space-y-1.5 max-h-36 overflow-y-auto">
											{allCustomerContacts.map((contact) => (
												<div
													key={contact.email}
													className="flex items-center space-x-2"
												>
													<input
														type="checkbox"
														id={`customer-${contact.email}`}
														checked={selectedCustomers.includes(
															contact.email || "",
														)}
														onChange={(e) => {
															const email = contact.email || "";
															if (e.target.checked) {
																setSelectedCustomers((prev) => [
																	...prev,
																	email,
																]);
															} else {
																setSelectedCustomers((prev) =>
																	prev.filter((em) => em !== email),
																);
															}
														}}
														className="h-4 w-4 text-yellow-500 rounded border-gray-600 focus:ring-yellow-500 focus:ring-opacity-25 bg-gray-700"
													/>
													<label
														htmlFor={`customer-${contact.email}`}
														className="text-sm text-gray-300 flex-1"
													>
														<div className="font-medium flex items-center gap-2">
															{contact.name}
															{contact.istEmailAnrede && (
																<span className="text-xs text-blue-400 bg-blue-900/50 px-1.5 py-0.5 rounded">
																	E-Mail-Anrede
																</span>
															)}
															{type === "lieferschein" &&
																contact.istEmailLieferscheinEmpfaenger && (
																	<span className="text-xs text-yellow-400 bg-yellow-900/50 px-1.5 py-0.5 rounded">
																		Lieferschein-Empfänger
																	</span>
																)}
															{type === "uebersicht" &&
																contact.istEmailUebersichtEmpfaenger && (
																	<span className="text-xs text-yellow-400 bg-yellow-900/50 px-1.5 py-0.5 rounded">
																		Übersicht-Empfänger
																	</span>
																)}
														</div>
														<div className="text-xs text-gray-400">
															{contact.email}
														</div>
													</label>
												</div>
											))}
										</div>
									</div>
								)}

								{!hasEmailRecipients && (
									<div className="bg-amber-900/30 border border-amber-700/50 p-3 rounded-md">
										<p className="text-amber-300 text-sm">
											⚠️ Warnung: Kein Ansprechpartner mit E-Mail gefunden.
										</p>
									</div>
								)}
							</div>
						</div>
					</div>

					{/* Document Details Section */}
					<div className="mt-4 pt-3 border-t border-gray-700">
						{type === "lieferschein" && lieferscheinDetails?.lieferschein && (
							<div className="bg-gray-700/30 p-3 rounded-md">
								<h5 className="text-sm font-medium text-gray-300 mb-2">
									Dokument-Details
								</h5>
								<div className="grid grid-cols-2 gap-4 text-xs text-gray-400">
									<div>
										<span className="font-medium">Lieferschein:</span>
										<br />
										{lieferscheinDetails.lieferschein.nummer ||
											"Ohne Nummer (Entwurf)"}
									</div>
									<div>
										<span className="font-medium">Datum:</span>
										<br />
										{new Date(
											lieferscheinDetails.lieferschein.erstelltAm,
										).toLocaleDateString("de-DE")}
									</div>
								</div>
							</div>
						)}

						{type === "uebersicht" && kunde && zeitraum && (
							<div className="bg-gray-700/30 p-3 rounded-md">
								<h5 className="text-sm font-medium text-gray-300 mb-2">
									Dokument-Details
								</h5>
								<div className="grid grid-cols-2 gap-4 text-xs text-gray-400">
									<div>
										<span className="font-medium">Kunde:</span>
										<br />
										{kunde.name}
									</div>
									<div>
										<span className="font-medium">Zeitraum:</span>
										<br />
										{zeitraum}
									</div>
								</div>
							</div>
						)}
					</div>
				</div>

				<div className="flex justify-end gap-2 p-4 bg-gray-800 border-t border-gray-700">
					<button
						onClick={onClose}
						className="px-4 py-2 rounded-md bg-gray-700 text-gray-300 hover:bg-gray-600 focus:outline-none"
						disabled={isSubmitting}
					>
						Abbrechen
					</button>
					<button
						onClick={handleSubmit}
						className="px-4 py-2 rounded-md bg-blue-600 text-white hover:bg-blue-500 focus:outline-none disabled:opacity-50 disabled:cursor-not-allowed flex items-center"
						disabled={
							isSubmitting ||
							!emailGreetingContact ||
							selectedEmployees.length === 0
						}
					>
						{isSubmitting ? (
							<>
								<svg
									className="animate-spin -ml-1 mr-2 h-4 w-4 text-white"
									xmlns="http://www.w3.org/2000/svg"
									fill="none"
									viewBox="0 0 24 24"
								>
									<circle
										className="opacity-25"
										cx="12"
										cy="12"
										r="10"
										stroke="currentColor"
										strokeWidth="4"
									></circle>
									<path
										className="opacity-75"
										fill="currentColor"
										d="M4 12a8 8 0 018-8V0C5.373 0 0 5.373 0 12h4zm2 5.291A7.962 7.962 0 014 12H0c0 3.042 1.135 5.824 3 7.938l3-2.647z"
									></path>
								</svg>
								Wird gesendet...
							</>
						) : (
							<>
								<CheckCircle className="h-4 w-4 mr-1.5" />
								Senden
							</>
						)}
					</button>
				</div>
			</div>
		</div>
	);
}
