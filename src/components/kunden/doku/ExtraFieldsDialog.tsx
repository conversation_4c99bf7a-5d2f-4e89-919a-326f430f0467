import { Button } from "@/components/_shared/Button";
import { Checkbox } from "@/components/_shared/Checkbox";
import { Input } from "@/components/_shared/Input";
import { ModalFormDialog } from "@/components/_shared/ModalFormDialog";
import { Textarea } from "@/components/_shared/Textarea";
import {
	Select,
	SelectContent,
	SelectItem,
	SelectTrigger,
	SelectValue,
} from "@/components/_shared/select";
import { FIELD_TYPES } from "@/../convex/schema";
import { Copy, Eye, EyeOff, FileText, X } from "lucide-react";
import { useState } from "react";
import { toast } from "sonner";

interface ExtraFieldsDialogField {
	label: string;
	value: string | number | null | undefined;
	fieldType?: string;
	isRequired?: boolean;
	isHidden?: boolean;
	isCopyable?: boolean;
	options?: string[];
}

interface ExtraFieldsDialogProps {
	isOpen: boolean;
	onClose: () => void;
	title: string;
	fields: ExtraFieldsDialogField[];
}

export function ExtraFieldsDialog({
	isOpen,
	onClose,
	title,
	fields,
}: ExtraFieldsDialogProps) {
	const [fieldVisibility, setFieldVisibility] = useState<
		Record<string, boolean>
	>({});

	// Toggle field visibility for password fields
	const toggleFieldVisibility = (fieldName: string) => {
		setFieldVisibility((prev) => ({
			...prev,
			[fieldName]: !prev[fieldName],
		}));
	};

	// Render field display based on type
	const renderFieldDisplay = (field: ExtraFieldsDialogField, index: number) => {
		const value =
			field.value !== undefined && field.value !== null
				? String(field.value)
				: "";
		const fieldType = field.fieldType || FIELD_TYPES.TEXT;
		const fieldKey = `${field.label}-${index}`;

		switch (fieldType) {
			case FIELD_TYPES.TEXTAREA:
				return (
					<Textarea
						value={value}
						readOnly
						className="min-h-[80px] bg-gray-800/50 border-gray-700 text-sm resize-none cursor-default"
					/>
				);
			case FIELD_TYPES.PASSWORD:
				return (
					<div className="relative">
						<Input
							type={fieldVisibility[fieldKey] ? "text" : "password"}
							value={value}
							readOnly
							className={`${field.isCopyable ? "pr-16" : "pr-9"} bg-gray-800/50 border-gray-700 text-sm h-9 cursor-default`}
						/>
						<div className="absolute right-0.5 top-0.5 flex gap-0.5">
							{field.isCopyable && value && (
								<Button
									type="button"
									variant="ghost"
									size="icon"
									className="h-7 w-7 text-gray-400 hover:text-white hover:bg-gray-700/70"
									onClick={() => {
										navigator.clipboard.writeText(value);
										toast.success("Wert in die Zwischenablage kopiert");
									}}
									title="Wert kopieren"
								>
									<Copy className="h-3.5 w-3.5" />
								</Button>
							)}
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="h-7 w-7 text-gray-400 hover:text-white hover:bg-gray-700/70"
								onClick={() => toggleFieldVisibility(fieldKey)}
								title={
									fieldVisibility[fieldKey]
										? "Passwort verbergen"
										: "Passwort anzeigen"
								}
							>
								{fieldVisibility[fieldKey] ? (
									<EyeOff className="h-3.5 w-3.5" />
								) : (
									<Eye className="h-3.5 w-3.5" />
								)}
							</Button>
						</div>
					</div>
				);
			case FIELD_TYPES.CHECKBOX:
				return (
					<div className="flex items-center space-x-2">
						<Checkbox
							checked={value === "true" || value === "1"}
							disabled
							className="cursor-default"
						/>
						<span className="text-sm text-gray-300">
							{value === "true" || value === "1" ? "Ja" : "Nein"}
						</span>
					</div>
				);
			case FIELD_TYPES.SELECT:
				return (
					<Select value={value} disabled>
						<SelectTrigger className="bg-gray-800/50 border-gray-700 text-sm h-9 cursor-default">
							<SelectValue />
						</SelectTrigger>
						<SelectContent>
							{field.options?.map((option) => (
								<SelectItem key={option} value={option}>
									{option}
								</SelectItem>
							))}
						</SelectContent>
					</Select>
				);
			case FIELD_TYPES.NUMBER:
				return (
					<div className="relative">
						<Input
							type="number"
							value={value}
							readOnly
							className={`${field.isCopyable ? "pr-9" : ""} bg-gray-800/50 border-gray-700 text-sm h-9 cursor-default`}
						/>
						{field.isCopyable && value && (
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-0.5 top-0.5 h-7 w-7 text-gray-400 hover:text-white hover:bg-gray-700/70"
								onClick={() => {
									navigator.clipboard.writeText(value);
									toast.success("Wert in die Zwischenablage kopiert");
								}}
								title="Wert kopieren"
							>
								<Copy className="h-3.5 w-3.5" />
							</Button>
						)}
					</div>
				);
			case FIELD_TYPES.EMAIL:
				return (
					<div className="relative">
						<Input
							type="email"
							value={value}
							readOnly
							className={`${field.isCopyable ? "pr-9" : ""} bg-gray-800/50 border-gray-700 text-sm h-9 cursor-default`}
						/>
						{field.isCopyable && value && (
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-0.5 top-0.5 h-7 w-7 text-gray-400 hover:text-white hover:bg-gray-700/70"
								onClick={() => {
									navigator.clipboard.writeText(value);
									toast.success("Wert in die Zwischenablage kopiert");
								}}
								title="Wert kopieren"
							>
								<Copy className="h-3.5 w-3.5" />
							</Button>
						)}
					</div>
				);
			case FIELD_TYPES.URL:
				return (
					<div className="relative">
						<Input
							type="url"
							value={value}
							readOnly
							className={`${field.isCopyable ? "pr-9" : ""} bg-gray-800/50 border-gray-700 text-sm h-9 cursor-default`}
						/>
						{field.isCopyable && value && (
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-0.5 top-0.5 h-7 w-7 text-gray-400 hover:text-white hover:bg-gray-700/70"
								onClick={() => {
									navigator.clipboard.writeText(value);
									toast.success("Wert in die Zwischenablage kopiert");
								}}
								title="Wert kopieren"
							>
								<Copy className="h-3.5 w-3.5" />
							</Button>
						)}
					</div>
				);
			default:
				return (
					<div className="relative">
						<Input
							type={fieldType === FIELD_TYPES.NUMBER ? "number" : "text"}
							value={value}
							readOnly
							className={`${field.isCopyable ? "pr-9" : ""} bg-gray-800/50 border-gray-700 text-sm h-9 cursor-default`}
						/>
						{field.isCopyable && value && (
							<Button
								type="button"
								variant="ghost"
								size="icon"
								className="absolute right-0.5 top-0.5 h-7 w-7 text-gray-400 hover:text-white hover:bg-gray-700/70"
								onClick={() => {
									navigator.clipboard.writeText(value);
									toast.success("Wert in die Zwischenablage kopiert");
								}}
								title="Wert kopieren"
							>
								<Copy className="h-3.5 w-3.5" />
							</Button>
						)}
					</div>
				);
		}
	};

	return (
		<ModalFormDialog
			isOpen={isOpen}
			onClose={onClose}
			title={title}
			icon={<FileText className="h-3.5 w-3.5" />}
			maxWidth="lg"
		>
			<div className="space-y-3">
				{fields.map((field, index) => (
					<div key={index} className="flex flex-col gap-1.5">
						<div className="text-xs font-medium text-gray-400">
							{field.label}
							{field.isRequired && <span className="text-red-500 ml-1">*</span>}
							<span className="text-purple-400 ml-1">*</span>
						</div>
						{renderFieldDisplay(field, index)}
					</div>
				))}
			</div>
		</ModalFormDialog>
	);
}
