import type { Doc, Id } from "@/../convex/_generated/dataModel";
import { FIELD_TYPES } from "@/../convex/schema";
import { Button } from "@/components/_shared/Button";
import {
	Card,
	CardContent,
	CardHeader,
	CardTitle,
} from "@/components/_shared/Card";
import { ExtraFieldsDialog } from "./ExtraFieldsDialog";
import {
	Table,
	TableBody,
	TableCell,
	TableHead,
	TableHeader,
	TableRow,
} from "@/components/_shared/Table";
import {
	Copy,
	Eye,
	EyeOff,
	NotebookText,
	Pencil,
	PlusCircle,
	Trash2,
	X,
} from "lucide-react";
import { useMemo, useState } from "react";
import { toast } from "sonner";

// Define DokuEintragMitInfo to match the output of getByKunde which includes categoryName and kundeName
interface DokuEintragMitInfo {
	_id: Id<"kunden_dokumentation">;
	_creationTime: number;
	kundenId: Id<"kunden">;
	kategorieID: number; // Numeric ID of the category
	feldwerte: { feldId: number; feldWert: string; feldName?: string }[];
	kategorieName: string; // Added from getByKunde
}

interface DokuCategorySectionProps {
	kategorie: Doc<"system_doku_kategorien">;
	alleEintraegeFuerKunde: DokuEintragMitInfo[];
	showFormForKategorie: Id<"system_doku_kategorien"> | null;
	onAddEntry: (kategorieId: Id<"system_doku_kategorien">) => void;
	onEditEntry: (
		kategorieId: Id<"system_doku_kategorien">,
		eintragId: Id<"kunden_dokumentation">,
	) => void;
	onCancelForm: () => void;
	onDeleteEntry: (id: Id<"kunden_dokumentation">) => Promise<void>;
	children?: React.ReactNode; // For DokuEntryForm
}

export function DokuCategorySection({
	kategorie,
	alleEintraegeFuerKunde,
	showFormForKategorie,
	onAddEntry,
	onEditEntry,
	onCancelForm,
	onDeleteEntry,
	children,
}: DokuCategorySectionProps) {
	const [hiddenFieldsVisibility, setHiddenFieldsVisibility] = useState<
		Record<string, boolean>
	>({});
	const [passwordCopiedStates, setPasswordCopiedStates] = useState<
		Record<string, boolean>
	>({});
	const [copiedStates, setCopiedStates] = useState<Record<string, boolean>>({});
	const [extraFieldsDialogOpen, setExtraFieldsDialogOpen] = useState(false);
	const [currentExtraFieldsEntry, setCurrentExtraFieldsEntry] =
		useState<DokuEintragMitInfo | null>(null);

	// Filter entries for the current category from all entries fetched for the customer
	const entriesForCurrentCategory = useMemo(
		() =>
			alleEintraegeFuerKunde.filter(
				(e: DokuEintragMitInfo) => e.kategorieID === kategorie.kategorieID,
			),
		[alleEintraegeFuerKunde, kategorie.kategorieID],
	);

	const copyToClipboard = (text: string, id: string, isPassword = false) => {
		navigator.clipboard.writeText(text).then(
			() => {
				if (isPassword) {
					setPasswordCopiedStates((prev) => ({ ...prev, [id]: true }));
					toast.success("Passwort kopiert!");
					setTimeout(
						() => setPasswordCopiedStates((prev) => ({ ...prev, [id]: false })),
						1500,
					);
				} else {
					setCopiedStates((prev) => ({ ...prev, [id]: true }));
					toast.success("In Zwischenablage kopiert!");
					setTimeout(
						() => setCopiedStates((prev) => ({ ...prev, [id]: false })),
						1500,
					);
				}
			},
			(err) => {
				toast.error("Fehler beim Kopieren.");
			},
		);
	};

	const toggleHiddenFieldsVisibility = (kategorieId: string) => {
		setHiddenFieldsVisibility((prev) => ({
			...prev,
			[kategorieId]: !prev[kategorieId],
		}));
	};

	return (
		<Card key={kategorie._id} className="shadow-lg border-0 overflow-hidden">
			<CardHeader className="pb-2.5 px-4 border-b border-gray-700/50 flex flex-row justify-between items-center">
				<CardTitle className="text-base font-medium">
					{kategorie.name}
				</CardTitle>
				<div className="flex items-center gap-1">
					{/* Eye button only if category has fields with istVersteckt=true AND form is NOT shown */}
					{!showFormForKategorie &&
						kategorie.felder.some((f) => f.istVersteckt) && (
							<Button
								variant="ghost"
								size="icon"
								onClick={() => toggleHiddenFieldsVisibility(kategorie._id)}
								className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
								title={
									hiddenFieldsVisibility[kategorie._id]
										? "Versteckte Felder ausblenden"
										: "Versteckte Felder anzeigen"
								}
							>
								{hiddenFieldsVisibility[kategorie._id] ? (
									<EyeOff className="h-3 w-3" />
								) : (
									<Eye className="h-3 w-3" />
								)}
							</Button>
						)}
					{/* Action Buttons (Add/Close) */}
					{showFormForKategorie === kategorie._id ? (
						// Close button
						<Button
							onClick={onCancelForm}
							size="icon"
							variant="ghost"
							className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
							title="Formular schließen"
						>
							<X className="h-3 w-3" />
						</Button>
					) : (
						// Add button
						<Button
							onClick={() => onAddEntry(kategorie._id)}
							size="icon"
							variant="ghost"
							className="h-6 w-6 text-gray-400 hover:text-white hover:bg-gray-700/70"
							title={`Eintrag zu "${kategorie.name}" hinzufügen`}
						>
							<PlusCircle className="h-3 w-3" />
						</Button>
					)}
				</div>
			</CardHeader>
			{/* Only render CardContent if there are entries OR if the form is shown for this category */}
			{(entriesForCurrentCategory.length > 0 ||
				showFormForKategorie === kategorie._id) && (
				<CardContent className="p-0">
					{showFormForKategorie === kategorie._id ? (
						<div className="p-4">{children}</div>
					) : (
						<Table>
							<TableHeader>
								<TableRow className="bg-gray-800/30 hover:bg-gray-800/30">
									{kategorie.felder
										.filter((feld) => !feld.istExtrafeld)
										.map((feld) => (
											<TableHead
												key={feld.feldId}
												className="py-1.5 px-2.5 text-xs"
											>
												{feld.name}
												{feld.istErforderlich && (
													<span className="text-red-400 ml-0.5">*</span>
												)}
											</TableHead>
										))}
									<TableHead className="w-20 text-center py-1.5 px-2.5 text-xs">
										Aktionen
									</TableHead>
								</TableRow>
							</TableHeader>
							<TableBody>
								{entriesForCurrentCategory.map((eintrag) => (
									<TableRow key={eintrag._id}>
										{kategorie.felder
											.filter((feld) => !feld.istExtrafeld)
											.map((feld) => {
												const feldWertObj = eintrag.feldwerte.find(
													(fw) => fw.feldId === feld.feldId,
												);
												const displayValue = feldWertObj?.feldWert || "-";
												const fieldType = feld.typ as keyof typeof FIELD_TYPES;
												const isHiddenVisible =
													hiddenFieldsVisibility[kategorie._id];
												const pwdCopyId = `pwd-${eintrag._id}-${feld.feldId}`;

												return (
													<TableCell
														key={feld.feldId}
														className="align-top py-1 px-2.5 text-xs"
													>
														{fieldType === FIELD_TYPES.PASSWORD ||
														feld.istVersteckt ? (
															<div className="flex items-center gap-1">
																<span className="italic text-gray-500">
																	{isHiddenVisible ? displayValue : "********"}
																</span>
																<Button
																	variant="ghost"
																	size="icon"
																	onClick={() =>
																		copyToClipboard(
																			displayValue,
																			pwdCopyId,
																			true,
																		)
																	}
																	className={`h-6 w-6 ${passwordCopiedStates[pwdCopyId] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																	title="Wert kopieren"
																>
																	<Copy className="h-3 w-3" />
																</Button>
															</div>
														) : fieldType === FIELD_TYPES.URL ? (
															<div className="flex items-center gap-1">
																<a
																	href={
																		displayValue.startsWith("http")
																			? displayValue
																			: `https://${displayValue}`
																	}
																	target="_blank"
																	rel="noopener noreferrer"
																	className="text-cyan-400 hover:text-cyan-300"
																>
																	{displayValue}
																</a>
																{feld.istKopierbar && displayValue !== "-" && (
																	<Button
																		variant="ghost"
																		size="icon"
																		onClick={() =>
																			copyToClipboard(
																				displayValue,
																				`field-${eintrag._id}-${feld.feldId}`,
																			)
																		}
																		className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																		title="Wert kopieren"
																	>
																		<Copy className="h-3 w-3" />
																	</Button>
																)}
															</div>
														) : fieldType === FIELD_TYPES.EMAIL ? (
															<div className="flex items-center gap-1">
																<a
																	href={`mailto:${displayValue}`}
																	className="text-cyan-400 hover:text-cyan-300"
																>
																	{displayValue}
																</a>
																{feld.istKopierbar && displayValue !== "-" && (
																	<Button
																		variant="ghost"
																		size="icon"
																		onClick={() =>
																			copyToClipboard(
																				displayValue,
																				`field-${eintrag._id}-${feld.feldId}`,
																			)
																		}
																		className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																		title="Wert kopieren"
																	>
																		<Copy className="h-3 w-3" />
																	</Button>
																)}
															</div>
														) : fieldType === FIELD_TYPES.TEXTAREA ? (
															<div className="flex items-start gap-1">
																<pre className="whitespace-pre-wrap text-xs font-sans">
																	{displayValue}
																</pre>
																{feld.istKopierbar && displayValue !== "-" && (
																	<Button
																		variant="ghost"
																		size="icon"
																		onClick={() =>
																			copyToClipboard(
																				displayValue,
																				`field-${eintrag._id}-${feld.feldId}`,
																			)
																		}
																		className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																		title="Wert kopieren"
																	>
																		<Copy className="h-3 w-3" />
																	</Button>
																)}
															</div>
														) : (
															<div className="flex items-center gap-1">
																<span>{displayValue}</span>
																{feld.istKopierbar && displayValue !== "-" && (
																	<Button
																		variant="ghost"
																		size="icon"
																		onClick={() =>
																			copyToClipboard(
																				displayValue,
																				`field-${eintrag._id}-${feld.feldId}`,
																			)
																		}
																		className={`h-6 w-6 ${copiedStates[`field-${eintrag._id}-${feld.feldId}`] ? "text-green-400" : "text-blue-400 hover:text-blue-500"} hover:bg-blue-500/10 shrink-0`}
																		title="Wert kopieren"
																	>
																		<Copy className="h-3 w-3" />
																	</Button>
																)}
															</div>
														)}
													</TableCell>
												);
											})}

										<TableCell className="text-center align-middle py-1 px-2.5 text-xs">
											<div className="flex justify-center gap-1">
												{/* Extrafelder-Button, nur anzeigen wenn es Extrafelder gibt */}
												{kategorie.felder.some((feld) => feld.istExtrafeld) &&
													eintrag.feldwerte.some((fw) => {
														const feld = kategorie.felder.find(
															(f) => f.feldId === fw.feldId,
														);
														return (
															feld?.istExtrafeld &&
															fw.feldWert &&
															fw.feldWert.trim() !== ""
														);
													}) && (
														<Button
															variant="ghost"
															size="icon"
															className="h-6 w-6 text-purple-400 hover:text-purple-500 hover:bg-purple-500/10"
															title="Extrafelder anzeigen"
															onClick={() => {
																setCurrentExtraFieldsEntry(eintrag);
																setExtraFieldsDialogOpen(true);
															}}
														>
															<NotebookText className="h-3 w-3" />
														</Button>
													)}
												<Button
													variant="ghost"
													size="icon"
													onClick={() =>
														onEditEntry(kategorie._id, eintrag._id)
													}
													className="h-6 w-6 text-blue-400 hover:text-blue-500 hover:bg-blue-500/10"
													title="Eintrag bearbeiten"
												>
													<Pencil className="h-3 w-3" />
												</Button>
												<Button
													variant="ghost"
													size="icon"
													onClick={() => onDeleteEntry(eintrag._id)}
													className="h-6 w-6 text-red-400 hover:text-red-500 hover:bg-red-500/10"
													title="Eintrag löschen"
												>
													<Trash2 className="h-3 w-3" />
												</Button>
											</div>
										</TableCell>
									</TableRow>
								))}
							</TableBody>
						</Table>
					)}
				</CardContent>
			)}

			{/* ExtraFields Dialog */}
			{extraFieldsDialogOpen && currentExtraFieldsEntry && (
				<ExtraFieldsDialog
					isOpen={extraFieldsDialogOpen}
					onClose={() => {
						setExtraFieldsDialogOpen(false);
						setCurrentExtraFieldsEntry(null);
					}}
					title="Extrafelder"
					fields={kategorie.felder
						.filter((feld) => feld.istExtrafeld)
						.map((feld) => {
							const feldWertObj = currentExtraFieldsEntry.feldwerte.find(
								(fw) => fw.feldId === feld.feldId,
							);
							return {
								label: feld.name,
								value: feldWertObj?.feldWert || "",
								fieldType: feld.typ,
								isRequired: feld.istErforderlich,
								isCopyable: feld.istKopierbar,
								options: feld.optionen,
							};
						})
						.filter((field) => field.value && field.value.trim() !== "")}
				/>
			)}
		</Card>
	);
}
